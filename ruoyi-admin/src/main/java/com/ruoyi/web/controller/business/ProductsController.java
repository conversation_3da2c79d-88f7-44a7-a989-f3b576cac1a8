package com.ruoyi.web.controller.business;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.business.domain.Products;
import com.ruoyi.business.service.IProductsService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 商品基本信息Controller
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@Controller
@RequestMapping("/business/products")
public class ProductsController extends BaseController
{
    private String prefix = "business/products";

    @Autowired
    private IProductsService productsService;

    @RequiresPermissions("business:products:view")
    @GetMapping()
    public String products()
    {
        return prefix + "/products";
    }

    /**
     * 查询商品基本信息列表
     */
    @RequiresPermissions("business:products:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(Products products)
    {
        startPage();
        List<Products> list = productsService.selectProductsList(products);
        return getDataTable(list);
    }

    /**
     * 导出商品基本信息列表
     */
    @RequiresPermissions("business:products:export")
    @Log(title = "商品基本信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(Products products)
    {
        List<Products> list = productsService.selectProductsList(products);
        ExcelUtil<Products> util = new ExcelUtil<Products>(Products.class);
        return util.exportExcel(list, "商品基本信息数据");
    }

    /**
     * 新增商品基本信息
     */
    @RequiresPermissions("business:products:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存商品基本信息
     */
    @RequiresPermissions("business:products:add")
    @Log(title = "商品基本信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(Products products)
    {
        return toAjax(productsService.insertProducts(products));
    }

    /**
     * 修改商品基本信息
     */
    @RequiresPermissions("business:products:edit")
    @GetMapping("/edit/{productId}")
    public String edit(@PathVariable("productId") Long productId, ModelMap mmap)
    {
        Products products = productsService.selectProductsByProductId(productId);
        mmap.put("products", products);
        return prefix + "/edit";
    }

    /**
     * 修改保存商品基本信息
     */
    @RequiresPermissions("business:products:edit")
    @Log(title = "商品基本信息", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(Products products)
    {
        return toAjax(productsService.updateProducts(products));
    }

    /**
     * 删除商品基本信息
     */
    @RequiresPermissions("business:products:remove")
    @Log(title = "商品基本信息", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(productsService.deleteProductsByProductIds(ids));
    }
}
