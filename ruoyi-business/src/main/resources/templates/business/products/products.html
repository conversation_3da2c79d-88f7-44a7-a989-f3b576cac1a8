<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('商品基本信息列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>商品名称：</label>
                                <input type="text" name="name"/>
                            </li>
                            <li>
                                <label>商品分类：</label>
                                <select name="category" th:with="type=${@dict.getType('product_category')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <label>商品单价：</label>
                                <input type="text" name="unitPrice"/>
                            </li>
                            <li>
                                <label>计量单位：</label>
                                <select name="unit" th:with="type=${@dict.getType('product_unit')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <label>创建时间：</label>
                                <input type="text" class="time-input" placeholder="请选择创建时间" name="createdAt"/>
                            </li>
                            <li>
                                <label>最后更新时间：</label>
                                <input type="text" class="time-input" placeholder="请选择最后更新时间" name="updatedAt"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="business:products:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="business:products:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="business:products:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="business:products:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('business:products:edit')}]];
        var removeFlag = [[${@permission.hasPermi('business:products:remove')}]];
        var categoryDatas = [[${@dict.getType('product_category')}]];
        var unitDatas = [[${@dict.getType('product_unit')}]];
        var prefix = ctx + "business/products";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "商品基本信息",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'productId',
                    title: '商品唯一标识符',
                    visible: false
                },
                {
                    field: 'name',
                    title: '商品名称'
                },
                {
                    field: 'description',
                    title: '商品详细描述'
                },
                {
                    field: 'category',
                    title: '商品分类',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(categoryDatas, value);
                    }
                },
                {
                    field: 'unitPrice',
                    title: '商品单价'
                },
                {
                    field: 'unit',
                    title: '计量单位',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(unitDatas, value);
                    }
                },
                {
                    field: 'createdAt',
                    title: '创建时间'
                },
                {
                    field: 'updatedAt',
                    title: '最后更新时间'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.productId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.productId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>