<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.business.mapper.ProductsMapper">
    
    <resultMap type="Products" id="ProductsResult">
        <result property="productId"    column="product_id"    />
        <result property="sku"    column="sku"    />
        <result property="name"    column="name"    />
        <result property="description"    column="description"    />
        <result property="category"    column="category"    />
        <result property="unitPrice"    column="unit_price"    />
        <result property="unit"    column="unit"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
    </resultMap>

    <sql id="selectProductsVo">
        select product_id, sku, name, description, category, unit_price, unit, created_at, updated_at from products
    </sql>

    <select id="selectProductsList" parameterType="Products" resultMap="ProductsResult">
        <include refid="selectProductsVo"/>
        <where>  
            <if test="sku != null  and sku != ''"> and sku = #{sku}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="category != null  and category != ''"> and category = #{category}</if>
            <if test="unitPrice != null "> and unit_price = #{unitPrice}</if>
            <if test="unit != null  and unit != ''"> and unit = #{unit}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
            <if test="updatedAt != null "> and updated_at = #{updatedAt}</if>
        </where>
    </select>
    
    <select id="selectProductsByProductId" parameterType="Long" resultMap="ProductsResult">
        <include refid="selectProductsVo"/>
        where product_id = #{productId}
    </select>

    <insert id="insertProducts" parameterType="Products" useGeneratedKeys="true" keyProperty="productId">
        insert into products
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sku != null and sku != ''">sku,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="description != null">description,</if>
            <if test="category != null and category != ''">category,</if>
            <if test="unitPrice != null">unit_price,</if>
            <if test="unit != null and unit != ''">unit,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sku != null and sku != ''">#{sku},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="description != null">#{description},</if>
            <if test="category != null and category != ''">#{category},</if>
            <if test="unitPrice != null">#{unitPrice},</if>
            <if test="unit != null and unit != ''">#{unit},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
         </trim>
    </insert>

    <update id="updateProducts" parameterType="Products">
        update products
        <trim prefix="SET" suffixOverrides=",">
            <if test="sku != null and sku != ''">sku = #{sku},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="description != null">description = #{description},</if>
            <if test="category != null and category != ''">category = #{category},</if>
            <if test="unitPrice != null">unit_price = #{unitPrice},</if>
            <if test="unit != null and unit != ''">unit = #{unit},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </trim>
        where product_id = #{productId}
    </update>

    <delete id="deleteProductsByProductId" parameterType="Long">
        delete from products where product_id = #{productId}
    </delete>

    <delete id="deleteProductsByProductIds" parameterType="String">
        delete from products where product_id in 
        <foreach item="productId" collection="array" open="(" separator="," close=")">
            #{productId}
        </foreach>
    </delete>

</mapper>