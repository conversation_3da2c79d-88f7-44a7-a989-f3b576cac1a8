package com.ruoyi.business.service;

import java.util.List;
import com.ruoyi.business.domain.Products;

/**
 * 商品基本信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface IProductsService 
{
    /**
     * 查询商品基本信息
     * 
     * @param productId 商品基本信息主键
     * @return 商品基本信息
     */
    public Products selectProductsByProductId(Long productId);

    /**
     * 查询商品基本信息列表
     * 
     * @param products 商品基本信息
     * @return 商品基本信息集合
     */
    public List<Products> selectProductsList(Products products);

    /**
     * 新增商品基本信息
     * 
     * @param products 商品基本信息
     * @return 结果
     */
    public int insertProducts(Products products);

    /**
     * 修改商品基本信息
     * 
     * @param products 商品基本信息
     * @return 结果
     */
    public int updateProducts(Products products);

    /**
     * 批量删除商品基本信息
     * 
     * @param productIds 需要删除的商品基本信息主键集合
     * @return 结果
     */
    public int deleteProductsByProductIds(String productIds);

    /**
     * 删除商品基本信息信息
     * 
     * @param productId 商品基本信息主键
     * @return 结果
     */
    public int deleteProductsByProductId(Long productId);
}
