package com.ruoyi.business.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.business.mapper.ProductsMapper;
import com.ruoyi.business.domain.Products;
import com.ruoyi.business.service.IProductsService;
import com.ruoyi.common.core.text.Convert;

/**
 * 商品基本信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@Service
public class ProductsServiceImpl implements IProductsService 
{
    @Autowired
    private ProductsMapper productsMapper;

    /**
     * 查询商品基本信息
     * 
     * @param productId 商品基本信息主键
     * @return 商品基本信息
     */
    @Override
    public Products selectProductsByProductId(Long productId)
    {
        return productsMapper.selectProductsByProductId(productId);
    }

    /**
     * 查询商品基本信息列表
     * 
     * @param products 商品基本信息
     * @return 商品基本信息
     */
    @Override
    public List<Products> selectProductsList(Products products)
    {
        return productsMapper.selectProductsList(products);
    }

    /**
     * 新增商品基本信息
     * 
     * @param products 商品基本信息
     * @return 结果
     */
    @Override
    public int insertProducts(Products products)
    {
        return productsMapper.insertProducts(products);
    }

    /**
     * 修改商品基本信息
     * 
     * @param products 商品基本信息
     * @return 结果
     */
    @Override
    public int updateProducts(Products products)
    {
        return productsMapper.updateProducts(products);
    }

    /**
     * 批量删除商品基本信息
     * 
     * @param productIds 需要删除的商品基本信息主键
     * @return 结果
     */
    @Override
    public int deleteProductsByProductIds(String productIds)
    {
        return productsMapper.deleteProductsByProductIds(Convert.toStrArray(productIds));
    }

    /**
     * 删除商品基本信息信息
     * 
     * @param productId 商品基本信息主键
     * @return 结果
     */
    @Override
    public int deleteProductsByProductId(Long productId)
    {
        return productsMapper.deleteProductsByProductId(productId);
    }
}
